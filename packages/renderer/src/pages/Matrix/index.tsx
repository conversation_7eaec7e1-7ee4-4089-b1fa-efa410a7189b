import React from 'react'
import { Outlet } from 'react-router'
import { type RouterTabItem, RouterTabs } from '@/components/RouterTabs'

const matrixTabs: RouterTabItem[] = [
  {
    value: 'summary',
    label: '数据统计',
    path: 'summary',
  },
  {
    value: 'distribution',
    label: '分发管理',
    path: 'distribution',
  },
  {
    value: 'auth',
    label: '账号授权',
    path: 'auth',
  },
  {
    value: 'list',
    label: '账号管理',
    path: 'list',
  },
  {
    value: 'draft',
    label: '草稿箱管理',
    path: 'draft',
  },
]

export const Matrix = () => {
  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex w-fit">
        <RouterTabs
          tabs={matrixTabs}
          basePath="/home/<USER>"
        />
      </div>
      <div className="flex flex-col flex-1 overflow-hidden ">
        <main className="flex-1 overflow-hidden mt-3 ">
          <Outlet />
        </main>
      </div>
    </div>
  )
}

