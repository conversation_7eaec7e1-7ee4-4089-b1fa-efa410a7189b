import { Injectable, Logger } from '@nestjs/common'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES, MixcutPipelines } from '@app/shared/types/mixcut.js'
import { OverlayType, VideoOverlay } from '@app/shared/types/overlay.js'
import { MixcutPipeline, MixcutPipelineContext } from './mixcut-pipeline.types.js'

import {
  VideoPositionOffsetProcessor,
  VideoRotationProcessor,
  VideoScaleProcessor,
  VideoTrimProcessor
} from './overlay-processors/index.js'
import {
  SimpleOverlaysFilterProcessingPipeline
} from '@/modules/mixcut/pipeline/pipelines/simple-overlays-filter-processing.js'
import { NarrationTextPositionPipeline } from '@/modules/mixcut/pipeline/pipelines/narration-text-position.js'

const videoOverlayFilter = (o: MixcutableOverlay) => o.type === OverlayType.VIDEO

const isOverlayFullScreen = (context: MixcutPipelineContext) => {
  const { playerMetadata: { width: playerWidth, height: playerHeight } } = context

  return (overlay: VideoOverlay): boolean => {
    // 只对未旋转的 overlay 应用旋转处理
    if (overlay.rotation !== 0) return false

    const overlayLeft = overlay.left
    const overlayTop = overlay.top
    const overlayRight = overlay.left + overlay.width
    const overlayBottom = overlay.top + overlay.height

    return (
      (overlayLeft <= 0 && overlayRight >= playerWidth)
      || (overlayTop <= 0 && overlayBottom >= playerHeight)
    )
  }
}

const PIPELINE_HANDLER_BY_NAME: Partial<Record<MixcutPipelines, (context: MixcutPipelineContext) => MixcutPipeline>> = {
  [MIXCUT_PIPELINES.video.rotation]: context => (
    SimpleOverlaysFilterProcessingPipeline.build(
      context,
      VideoRotationProcessor,
      videoOverlayFilter
    )
  ),
  [MIXCUT_PIPELINES.video.scale]: context => (
    SimpleOverlaysFilterProcessingPipeline.build(
      context,
      VideoScaleProcessor,
      videoOverlayFilter
    )
  ),
  [MIXCUT_PIPELINES.video.positionOffset]: context => (
    SimpleOverlaysFilterProcessingPipeline.build(
      context,
      VideoPositionOffsetProcessor,
      videoOverlayFilter
    )
  ),
  [MIXCUT_PIPELINES.video.trim]: context => (
    SimpleOverlaysFilterProcessingPipeline.build(
      context,
      VideoTrimProcessor,
      videoOverlayFilter
    )
  ),

  [MIXCUT_PIPELINES.narrationText.positionOffset]: context => new NarrationTextPositionPipeline(context)

  // [MIXCUT_PIPELINES.video.speed]: context => new SimpleOverlaysFilterProcessingPipeline(context).apply(VideoSpeedProcessor, videoOverlayFilter),
}

@Injectable()
export class MixcutPipelineService {

  private readonly logger = new Logger(MixcutPipelineService.name)

  public async process(
    context: MixcutPipelineContext,
    overlays: MixcutableOverlay[],
    pipelines: Array<{ name: MixcutPipelines, config?: any }>,
  ) {
    for (const pipeline of pipelines) {
      const processor = PIPELINE_HANDLER_BY_NAME[pipeline.name]?.(context)
      if (!processor) {
        this.logger.warn('No processor found by pipeline name: ' + pipeline.name)
        continue
      }

      overlays = await processor.process(overlays)
    }

    return overlays
  }
}
