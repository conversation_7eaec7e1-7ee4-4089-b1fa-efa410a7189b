import { SingleOverlayProcessor } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'

/**
* 随机截断视频开头和结尾的部分帧数
*/
export class VideoTrimProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly RANGE_MIN = 0
  private readonly RANGE_MAX = 15

  async process(overlay: VideoOverlay & MixcutableOverlay) {
    const { originalDurationFrames, trimStartFrames = 0, trimEndFrames = 0, durationInFrames } = overlay

    if (trimEndFrames > 0 && trimStartFrames > 0) return overlay

    const additionalTrimEnd = trimEndFrames
      ? 0
      : _.random(
        this.RANGE_MIN,
        Math.min((originalDurationFrames - trimStartFrames) / 3, this.RANGE_MAX)
      )

    const additionalTrimStart = trimStartFrames
      ? 0
      : _.random(
        this.RANGE_MIN,
        Math.min((originalDurationFrames - trimEndFrames) / 3, this.RANGE_MAX) - additionalTrimEnd
      )

    const newTrimStartFrames = trimStartFrames + additionalTrimStart
    const newTrimEndFrames = trimEndFrames + additionalTrimEnd

    const indeedPlayedFrames = originalDurationFrames - newTrimEndFrames - newTrimStartFrames

    // 更改变速, 以保证去除片尾后, 播放时长不变
    const newSpeed = indeedPlayedFrames / durationInFrames
    // console.log(`trimStart ${trimStartFrames} => ${newTrimStartFrames}; trimEnd ${trimEndFrames} => ${newTrimEndFrames}; speed ${speed} => ${newSpeed}`)

    return {
      ...overlay,
      trimStartFrames: newTrimStartFrames,
      trimEndFrames: newTrimEndFrames,
      speed: newSpeed
      // durationInFrames: durationInFrames - additionalTrimEnd / speed
    }
  }
}
