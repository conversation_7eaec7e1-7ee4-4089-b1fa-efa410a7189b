/*
/!**
 * 视频缩放
 * @description 每个视频随机放大素材画布比例
 *!/

/!**
 * 视频镜像
 * @description 将视频画面水平镜像
 *!/
export class VideoFlipProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const shouldFlip = Math.random() > 0.5

    return {
      ...overlay,
      styles: {
        ...overlay.styles,
        // TODO: 判断原始的状态
        transform: shouldFlip ? 'scaleX(-1)' : ''
      }
    }
  }
}

/!**
 * 分镜素材智能截取
 * @description 开启后，系统将分镜里的素材，如分镜时长5s，放入10秒的素材，将从10s的素材中智能随机选取5秒;若不开启则默认从头截取5秒
 *!/
export class VideoSmartClipProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 视频变速
 * @description 每个混剪视频整体时长随机进行1.1x-1.2x变速处理
 *!/
export class VideoSpeedProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly SPEED_CHANGE_RANGE = 1

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const speedChangeMultiplier = 1 + _.random(-this.SPEED_CHANGE_RANGE, this.SPEED_CHANGE_RANGE)

    const newSpeed = (overlay.speed ?? 1) * speedChangeMultiplier

    return {
      ...overlay,
      speed: newSpeed,
      durationInFrames: overlay.durationInFrames / speedChangeMultiplier
    }
  }
}

/!**
 * 透明蒙版
 * @description 给每个视频随机添加不同的透明蒙版 (在视频上面叠半透明视频)
 *!/
export class VideoMaskingProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 口播字幕字体
 * @description 为所有口播字幕统一更换字体
 *!/
export class NarrationFontFamilyProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 口播字幕花体字样式
 * @description 为所有口播字幕统一更换花体字样式
 *!/
export class NarrationStyledTextProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 全局文字字体
 *!/
export class GlobalTextFontFamilyProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 全局文字花体字样式
 *!/
export class GlobalTextStyledTextProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/!**
 * 口播音色
 * @description 为口播轨道中的音频重新选择音色并生成配音
 *!/
export class NarrationTimbreProcessor extends SingleOverlayProcessor<SoundOverlay> {

  async process(overlay: SoundOverlay): Promise<SoundOverlay> {
    // TODO
    return overlay
  }
}*/

export { VideoRotationProcessor } from './video-rotation.processor.js'
export { VideoScaleProcessor } from './video-scale.processor.js'
export { VideoPositionOffsetProcessor } from './video-position-offset.processor.js'
export { VideoTrimProcessor } from './video-trim.processor.js'
