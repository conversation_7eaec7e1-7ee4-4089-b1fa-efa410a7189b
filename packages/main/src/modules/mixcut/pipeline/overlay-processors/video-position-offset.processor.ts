import { SingleOverlayProcessor } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'

/**
* 随机偏移视频的位置，并补偿大小以保证填满画面
*/
export class VideoPositionOffsetProcessor extends SingleOverlayProcessor<VideoOverlay> {

  private readonly OFFSET_PERCENT_RANGE = 0.25

  async process(overlay: VideoOverlay & MixcutableOverlay) {
    const offsetPercent = _.random(0, this.OFFSET_PERCENT_RANGE)

    return {
      ...overlay,
      left: overlay.left - Math.round(overlay.width * offsetPercent),
      top: overlay.top - Math.round(overlay.height * offsetPercent),
      width: Math.round(overlay.width * (1 + offsetPercent)),
      height: Math.round(overlay.height * (1 + offsetPercent)),
    }
  }
}
