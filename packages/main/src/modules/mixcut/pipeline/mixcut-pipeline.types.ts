import { MixcutableOverlay, PlayerMetadata } from '@app/shared/types/ipc/mixcut.js'
import { Overlay } from '@app/shared/types/overlay.js'

export type MixcutPipelineContext = {
  playerMetadata: PlayerMetadata
}

export abstract class MixcutPipeline {

  constructor(public readonly context: MixcutPipelineContext) {
  }

  abstract process(overlays: MixcutableOverlay[]): Promise<MixcutableOverlay[]>
}

export abstract class SingleOverlayProcessor<TLimitedOverlay extends Overlay = Overlay> {

  constructor(public pipeline: MixcutPipeline) {
  }

  abstract process(overlay: TLimitedOverlay & MixcutableOverlay): Promise<TLimitedOverlay & MixcutableOverlay>
}
