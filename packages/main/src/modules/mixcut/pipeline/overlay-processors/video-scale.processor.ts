import { SingleOverlayProcessor } from '../mixcut-pipeline.types.js'
import { VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'

/**
 * 随机放大视频，同时补偿位移和宽高，以保证视频中心点不变的同时填满画面大小
 */
export class VideoScaleProcessor extends SingleOverlayProcessor<VideoOverlay> {

  private readonly RANGE = 0.2

  async process(overlay: VideoOverlay & MixcutableOverlay) {
    const scaleDelta = _.random(0, this.RANGE)

    return {
      ...overlay,
      width: Math.round(overlay.width * (1 + scaleDelta)),
      height: Math.round(overlay.height * (1 + scaleDelta)),
      left: overlay.left - overlay.width * scaleDelta / 2,
      top: overlay.top - overlay.height * scaleDelta / 2,
    }
  }
}
