
export const MIXCUT_PIPELINES = {
  video: {
    rotation: 'VIDEO_ROTATION',
    scale: 'VIDEO_SCALE',
    positionOffset: 'VIDEO_POSITION_OFFSET',
    flip: 'VIDEO_FLIP',
    smartClip: 'VIDEO_SMART_CLIP',
    speed: 'VIDEO_SPEED',
    trim: 'VIDEO_TRIM',
    masking: 'VIDEO_MASKING',
  },
  narrationText: {
    positionOffset: 'NARRATION_POSITION_OFFSET',
    fontFamily: 'NARRATION_FONT_FAMILY',
    styledText: 'NARRATION_STYLED_TEXT',
  },
  globalText: {
    fontFamily: 'GLOBAL_TEXT_FONT_FAMILY',
    styledText: 'GLOBAL_TEXT_STYLED_TEXT',
  },
  narrationSound: {
    timbre: 'NARRATION_TIMBRE',
  },
} as const

export type MixcutPipelines =
  | (typeof MIXCUT_PIPELINES.video)[keyof typeof MIXCUT_PIPELINES.video]
  | (typeof MIXCUT_PIPELINES.globalText)[keyof typeof MIXCUT_PIPELINES.globalText]
  | (typeof MIXCUT_PIPELINES.narrationText)[keyof typeof MIXCUT_PIPELINES.narrationText]
  | (typeof MIXCUT_PIPELINES.narrationSound)[keyof typeof MIXCUT_PIPELINES.narrationSound]
