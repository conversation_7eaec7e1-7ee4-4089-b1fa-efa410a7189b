import React, { <PERSON>psWithChildren, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Drawer, DrawerClose, Drawer<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { NumberInput, RangeInput } from '@/components/ui/number-input'
import { X } from 'lucide-react'
import { useMixcutContext } from '@/modules/mixcut/context/context'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'
import { MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'
import { MixcutRuleCheckbox } from '@/modules/mixcut/components/mixcut-rule-checkbox'
import { LabeledCheckbox } from '@/components/ui/checkbox'

type CategoryPanelComponent = React.FC<{ category: MixcutRulesFormCategories }>

const VideoTransform: CategoryPanelComponent = () => {
  const CATEGORY = MixcutRulesFormCategories.video

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.rotation}
          label="视频旋转"
        />

        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.scale}
          label="视频缩放"
        />

        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.positionOffset}
          label="视频位置偏移"
        />

        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.video.trim}
          label="视频去片头/片尾"
        >
          {({ category, pipeline, getConfigValue, setConfigValue }) => (
            <>
              <LabeledCheckbox
                id={`${category}-${pipeline}-allowTrimStart`}
                label="允许去片头"
                checked={getConfigValue('allowTrimStart')}
                onChange={checked => setConfigValue('allowTrimStart', checked)}
              />

              <RangeInput
                minValue={getConfigValue('rangeMin')}
                maxValue={getConfigValue('rangeMax')}
                onMinChange={val => setConfigValue('rangeMin', val)}
                onMaxChange={val => setConfigValue('rangeMax', val)}
              />
            </>
          )}
        </MixcutRuleCheckbox>
      </div>
    </div>
  )
}

const NarrationTextTransform: CategoryPanelComponent = () => {
  const CATEGORY = MixcutRulesFormCategories.narrationText

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.narrationText.positionOffset}
          label="口播字幕位置偏移"
        >
          {() => (
            <div />
          )}
        </MixcutRuleCheckbox>

        <MixcutRuleCheckbox
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.narrationText.fontFamily}
          label="口播字幕字体变换"
        />
      </div>
    </div>
  )
}

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const tabMap: Record<MixcutRulesFormCategories, { title: string, component: CategoryPanelComponent }> = {
    [MixcutRulesFormCategories.video]: { title: '视频变换处理', component: VideoTransform },
    [MixcutRulesFormCategories.narrationText]: { title: '口播字幕变换', component: NarrationTextTransform },
  }

  const tabs = Object
    .entries(tabMap)
    .map(([key, value]) => ({
      key,
      label: value.title,
      component: value.component,
    }))

  const [activeTab, setActiveTab] = useState(tabs[0].key)

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.key}
                value={tab.key}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full">
          {tabs.map(tab => (
            <TabsContent key={tab.key} value={tab.key} className="h-full">
              <div className="flex h-full text-muted-foreground">
                {tab.component && React.createElement(tab.component, { category: tab.key as MixcutRulesFormCategories })}
                {/*{tab.label} 内容区域*/}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
export const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  const {
    generation: {
      generateCount, drawerOpen,
      setGenerateCount, generateCombinations, setDrawerOpen
    }
  } = useMixcutContext()

  return (
    <Drawer direction="right" open={drawerOpen} onOpenChange={setDrawerOpen}>
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>

        {/* 底部生成数量控制 */}
        <div className="px-6 py-4 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-foreground">生成混剪视频数量:</span>
              <NumberInput
                value={generateCount}
                onChange={setGenerateCount}
                min={1}
                max={50}
                step={1}
                className="w-20"
              />
              <span className="text-xs text-muted-foreground">个</span>
            </div>
            <div className="text-xs text-muted-foreground">
              建议设置1-20个，数量过多可能影响生成速度
            </div>
          </div>

          <div className="flex justify-end">
            <Button variant="default" size="sm" onClick={generateCombinations}>
              生成混剪预览
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
