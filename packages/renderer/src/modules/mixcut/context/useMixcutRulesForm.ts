import { useForm, UseFormReturn } from 'react-hook-form'
import { MIXCUT_PIPELINES, MixcutPipelines } from '@app/shared/types/mixcut'
import { z } from 'zod'
import { mapValues } from 'lodash'

export enum MixcutRulesFormCategories {
  video = 'videoTransform',
  narrationText = 'narrationTextTransform',
  // globalBackgroundMusic = 'globalBackgroundMusic',
}

const baseItem = z.object({
  enabled: z.boolean().default(false),
})

const rangeItem = baseItem.extend({
  range: z.number().optional()
})

const videoFormItems = z.object({
  [MIXCUT_PIPELINES.video.rotation]: rangeItem,
  [MIXCUT_PIPELINES.video.scale]: rangeItem,
  [MIXCUT_PIPELINES.video.positionOffset]: rangeItem,
  [MIXCUT_PIPELINES.video.trim]: baseItem.extend({
    rangeMin: z.number().default(0),
    rangeMax: z.number().default(15),
    allowTrimStart: z.boolean().default(true),
  }),
  [MIXCUT_PIPELINES.video.flip]: baseItem,
  [MIXCUT_PIPELINES.video.smartClip]: baseItem,
  [MIXCUT_PIPELINES.video.speed]: baseItem,
  [MIXCUT_PIPELINES.video.masking]: baseItem,
})

const narrationTextFormItems = z.object({
  [MIXCUT_PIPELINES.narrationText.positionOffset]: baseItem.extend({
    allowHorizontalOffset: z.boolean().default(true)
  }),
  [MIXCUT_PIPELINES.narrationText.fontFamily]: baseItem,
})

export const formItemsByCategory = {
  [MixcutRulesFormCategories.video]: videoFormItems,
  [MixcutRulesFormCategories.narrationText]: narrationTextFormItems,
} as const

export type MixcutRulesFormValue = {
  [Category in MixcutRulesFormCategories]: /*z.infer<(typeof formItemsByCategory)[Category]> &*/ Record<
    MixcutPipelines,
    {
      enabled: boolean,
      [property: string]: any
    }
  >
}

export type MixcutRulesForm = UseFormReturn<MixcutRulesFormValue>

export const useMixcutRulesForm = () => {
  return useForm<MixcutRulesFormValue>({
    defaultValues: mapValues(formItemsByCategory, _schema => ({}))
  })
}
