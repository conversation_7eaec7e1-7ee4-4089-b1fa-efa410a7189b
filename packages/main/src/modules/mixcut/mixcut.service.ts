import { Inject, Injectable, Logger } from '@nestjs/common'
import { Combo, MixcutIPC, RenderRequestPayload } from '@app/shared/types/ipc/mixcut.js'
import { FileUploaderService } from '../file-uploader/file-uploader.service.js'
import { RequestService } from '../global/request.service.js'
import { ResourceService } from '../resource/resource.service.js'
import { v4 as uuidv4 } from 'uuid'
import { UploadModule } from '@app/shared/types/ipc/file-uploader.js'
import { similarityToRepetitionRate } from '@app/shared/utils.js'
import { MixcutEncryption } from './mixcut-encryption.util.js'
import fetch from 'node-fetch'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types.js'
import { promises as fsPromises } from 'fs'
import { RenderableOverlay } from '@app/shared/types/overlay.js'
import { MixcutPipelineService } from './pipeline/mixcut-pipeline.service.js'

const ZERO_SIMILARITY_COMBO_GENERATING_STRATEGY = 2

function generateFakeUrlForMixcut(id: string | number) {
  return `mixcut-cache://${id}`
}

@Injectable()
export class MixcutService implements MixcutIPC.Interface {

  private readonly logger = new Logger(MixcutService.name)

  constructor(
    @Inject(FileUploaderService) private fileUploaderService: FileUploaderService,
    @Inject(RequestService) private requestService: RequestService,
    @Inject(ResourceService) private resourceService: ResourceService,
    @Inject(MixcutPipelineService) private pipelineService: MixcutPipelineService,
  ) {
  }

  /**
   * 算法目标：
   * 1. 给定的 `matrix` 是一个二维数组，代表了每一列上可用的行序号。例如, [[0,1,2], [1,2]] 代表 第一列可用行号为0,1,2; 第二列可用行号为1,2
   * 2. 相似度的定义: 两个组合之间相似度，指他们在同样的位置上出现同样值的频次。例如, [0,1] 与 [0,2] 的相似度为 0.5。
   * 3. 计算一个组合的相似度，需要将它与先前的所有组合进行比较，取最大值。
   * 4. 生成的组合必须满足 `matrix` 的约束，即每一列上的值必须在 `matrix` 中的对应数组内。
   * 5. 生成的组合必须是唯一的，不能重复。
   * 6. 在开始生成之前，需要对 `limit` 的合法性进行校验，如果 `limit` 大于所有可能的组合数，则需要自动设置为最大值。
   * 7. 优先生成相似度为 0 的组合。例如给定的 matrix 为 [[0,1,2], [0,1,2], [0,1,2]], 则可以优先生成三个相似度为 0 的组合, 即 [0,0,0], [1,1,1], [2,2,2]
   *    当然，以上的三个并不是唯一的相似度的为 0 的组合，例如 [0,1,2], [1,2,0], [2,0,1] 也是相似度为 0 的组合。算法要同时允许这两种策略，通过一个参数来控制。
   * 8. 相似度为 0 的组合生成完之后，以随机生成的方式继续生成，直到组合总数达到 `limit` 数量为止。
   */
  public async generateCombos(props: { limit: number, threshold: number, matrix: number[][] }): Promise<Combo[]> {
    const { limit, threshold, matrix } = props

    const _checkSimilarity = (a: number[], b: number[]) => {
      let count = 0
      for (let i = 0; i < a.length; i++) {
        if (a[i] === b[i]) {
          count++
        }
      }
      return count / a.length
    }

    // 输入验证
    if (!matrix || matrix.length === 0) {
      return []
    }

    // 计算所有可能的组合总数
    const totalPossibleCombos = matrix.reduce((total, column) => total * column.length, 1)

    // 调整limit为合法值
    const actualLimit = Math.min(limit, totalPossibleCombos)

    if (actualLimit <= 0) {
      return []
    }

    const results: Combo[] = []
    const usedSelections = new Set<string>()

    // 辅助函数：计算组合与已有组合的最大相似度
    const getMaxSimilarity = (combo: number[]): number => {
      if (results.length === 0) return 0
      return Math.max(...results.map(existing => _checkSimilarity(combo, existing.selections)))
    }

    // 辅助函数：组合转字符串用于去重
    const comboToString = (combo: number[]): string => combo.join(',')

    // 辅助函数：添加组合到结果
    const addCombo = (combo: number[]): boolean => {
      const comboStr = comboToString(combo)
      if (usedSelections.has(comboStr)) {
        return false
      }

      const maxSimilarity = getMaxSimilarity(combo)
      if (maxSimilarity < threshold) {
        usedSelections.add(comboStr)
        results.push({
          selections: [...combo],
          similarity: maxSimilarity
        })
        return true
      }
      return false
    }

    // 策略1：横排策略 - 生成相似度为0的组合 [0,0,0], [1,1,1], [2,2,2]
    const generateHorizontalZeroSimilarity = (): void => {
      // 找到所有列的交集值
      const commonValues = matrix[0].filter(value =>
        matrix.every(column => column.includes(value))
      )

      for (const value of commonValues) {
        if (results.length >= actualLimit) break
        const combo = new Array(matrix.length).fill(value)
        addCombo(combo)
      }
    }

    // 策略2：轮换策略 - 生成相似度为0的组合 [0,1,2], [1,2,0], [2,0,1]
    const generateRotationZeroSimilarity = (): void => {
      // 找到最小列长度
      const minColumnLength = Math.min(...matrix.map(col => col.length))

      // 尝试生成轮换组合
      for (let offset = 0; offset < minColumnLength; offset++) {
        if (results.length >= actualLimit) break

        const combo: number[] = []
        let isValid = true

        for (let i = 0; i < matrix.length; i++) {
          const targetIndex = (i + offset) % minColumnLength
          if (targetIndex >= matrix[i].length) {
            isValid = false
            break
          }
          combo.push(matrix[i][targetIndex])
        }

        if (isValid) {
          addCombo(combo)
        }
      }
    }

    // 策略3：随机挑选相似度为0的组合
    const generateRandomZeroSimilarity = (): void => {
      const maxAttempts = Math.min(1000, totalPossibleCombos)
      let attempts = 0

      while (results.length < actualLimit && attempts < maxAttempts) {
        attempts++

        // 生成随机组合
        const combo = matrix.map(column =>
          column[Math.floor(Math.random() * column.length)]
        )

        // 检查是否与现有组合相似度为0
        if (results.length === 0 || getMaxSimilarity(combo) === 0) {
          if (addCombo(combo)) {
            attempts = 0 // 重置尝试次数
          }
        }
      }
    }

    // 第一阶段：生成相似度为0的组合
    [
      generateHorizontalZeroSimilarity,
      generateRotationZeroSimilarity,
      generateRandomZeroSimilarity
    ][ZERO_SIMILARITY_COMBO_GENERATING_STRATEGY]()

    // 第二阶段：随机生成剩余组合
    const maxRandomAttempts = Math.min(10000, totalPossibleCombos * 2)
    let randomAttempts = 0

    while (results.length < actualLimit && randomAttempts < maxRandomAttempts) {
      randomAttempts++

      // 生成随机组合
      const combo = matrix.map(column =>
        column[Math.floor(Math.random() * column.length)]
      )

      addCombo(combo)
    }

    return results
  }

  public async executeMixcut(props: MixcutIPC.ExecuteMixcutProps): Promise<RenderableOverlay[]> {
    return this.pipelineService.process(props.context, props.overlays, props.pipelines)
  }

  /**
   * 上传混剪结果到服务器
   * @param params 上传参数
   * @returns Promise<any> 上传结果
   */
  public async uploadMixcutResult(params: {
    scriptId: string
    data: RenderRequestPayload
    similarity: number
    cover?: string
    duration?: number
  }): Promise<any> {
    try {
      const module = UploadModule.remix
      const mixcutId = `mixcut-${uuidv4()}`

      const { scriptId, data, cover, duration, similarity } = params

      const jsonString = JSON.stringify(data)
      const buffer = Buffer.from(jsonString, 'utf8')

      const uploadResult = await this.fileUploaderService.uploadBufferToOSS({
        buffer: Array.from(buffer),
        fileName: `${mixcutId}.json`,
        module,
      })

      if (!uploadResult.success) {
        throw new Error(`上传预览数据失败: ${uploadResult.error}`)
      }

      if (!uploadResult.objectId) {
        throw new Error('上传结果中缺少 objectId')
      }

      const id = await this.requestService.post<string>('/app-api/creative/remix/preview/save', {
        scriptId,
        cover: cover,
        duration: duration || 0,
        repetitionRate: similarityToRepetitionRate(similarity),
        objectId: uploadResult.objectId
      })

      await this.resourceService.saveBufferResource(
        generateFakeUrlForMixcut(id),
        await MixcutEncryption.compressAndEncrypt(buffer),
        ResourceCacheType.MIXCUT,
      )

      return id
    } catch (error) {
      this.logger.error('上传混剪结果失败:', error)
      throw new Error(`上传混剪结果失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 缓存混剪数据到本地
   * @param params 缓存参数
   * @returns Promise<CacheMixcutResult> 缓存结果
   */
  public async cacheMixcutData(params: MixcutIPC.CacheMixcutParams): Promise<void> {
    for (const mixcut of params.mixcuts) {
      try {
        const { id, url } = mixcut
        const fakeUrl = generateFakeUrlForMixcut(id)
        const type = ResourceCacheType.MIXCUT

        if (await this.resourceService.isCached(fakeUrl, type)) {
          continue
        }

        if (!url) continue

        const downloadUrl = await this.requestService.parseUrlFromObjectHref(url)

        const response = await fetch(downloadUrl)
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status} ${response.statusText}`)
        }

        const jsonData = await response.json()

        const encryptedData = await MixcutEncryption.compressAndEncrypt(
          Buffer.from(JSON.stringify(jsonData))
        )

        await this.resourceService.saveBufferResource(
          fakeUrl,
          encryptedData,
          type,
        )
      } catch (error) {
        this.logger.error(`[cacheMixcutData] 缓存混剪 ${mixcut.id} 失败:`, error)
      }
    }
  }

  /**
   * 直接从本地缓存获取混剪数据
   * @param params.id 混剪 ID
   * @returns Promise<any | null> 解密后的数据，如果未缓存则返回 null
   */
  public async getMixcutDataFromCache(params: { id: number }): Promise<any | null> {
    try {
      const cacheUrl = generateFakeUrlForMixcut(params.id)

      const cacheEntry = await this.resourceService.getResource({ url: cacheUrl })

      if (!cacheEntry) {
        return null
      }

      // 读取加密文件
      const encryptedData = await fsPromises.readFile(cacheEntry.localPath)

      // 解密数据
      return await MixcutEncryption.decryptAndDecompress(encryptedData)
    } catch (error) {
      this.logger.error(`[getMixcutDataFromCache] 获取混剪缓存 ${params.id} 失败:`, error)
      return null
    }
  }

  public async clearMixcuts(...ids: number[]) {
    for (const id of ids) {
      const cacheUrl = generateFakeUrlForMixcut(id)
      await this.resourceService.removeResource(cacheUrl)
    }
  }
}
