import React from 'react'
import { useMixcutContext } from '@/modules/mixcut/context/context'
import { formItemsByCategory, MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'
import { LabeledCheckbox } from '@/components/ui/checkbox'
import { z, ZodObject } from 'zod'

type GetPipelineKeys<TCategory extends MixcutRulesFormCategories> = keyof (typeof formItemsByCategory)[TCategory]['shape']

type GetPipelineConfig<
  TCategory extends MixcutRulesFormCategories,
  TPipeline extends GetPipelineKeys<TCategory>
> = (typeof formItemsByCategory)[TCategory]['shape'][TPipeline]

interface MixcutRuleCheckboxProps<
  TCategory extends MixcutRulesFormCategories,
  TPipeline extends GetPipelineKeys<TCategory>
> {
  label: string
  category: TCategory
  pipeline: TPipeline,

  children?: (context: {
    category: TCategory
    pipeline: TPipeline,
    getConfigValue<
      Key extends (
        GetPipelineConfig<TCategory, TPipeline> extends ZodObject<any>
          ? keyof GetPipelineConfig<TCategory, TPipeline>['shape']
          : never
      ),
      Value extends (
        GetPipelineConfig<TCategory, TPipeline> extends ZodObject<any>
          ? z.infer<GetPipelineConfig<TCategory, TPipeline>>[Key]
          : never
      )
    >(
      key: Key
    ): Value
    setConfigValue<
      Key extends (
        GetPipelineConfig<TCategory, TPipeline> extends ZodObject<any>
          ? keyof GetPipelineConfig<TCategory, TPipeline>['shape']
          : never
      ),
      Value extends (
        GetPipelineConfig<TCategory, TPipeline> extends ZodObject<any>
          ? z.infer<GetPipelineConfig<TCategory, TPipeline>>[Key]
          : never
      )
    >(key: Key, value: Value): void
  }) => React.ReactNode
}

export function MixcutRuleCheckbox<
  TCategory extends MixcutRulesFormCategories,
  TPipeline extends GetPipelineKeys<TCategory>
>({
  label, pipeline: _pipeline, category, children
}: MixcutRuleCheckboxProps<TCategory, TPipeline>) {
  const pipeline = _pipeline as unknown as string

  const { generation: { rulesForm: { watch, setValue } } } = useMixcutContext()

  const formKey = `${category}.${pipeline}.enabled` as any
  const formValue = watch(formKey)
  const checkBoxId = `${category}-${pipeline}-checkbox`

  const checked = formValue || false

  return (
    <>
      <div className="flex space-x-2">
        <LabeledCheckbox
          id={checkBoxId}
          checked={checked}
          onChange={(checked: boolean) => {
            setValue(formKey, checked)
          }}
          label={label}
        />
      </div>

      {checked && children && typeof children === 'function' && (
        <div className="pl-6">
          {children({
            pipeline: _pipeline,
            category,
            getConfigValue(key) {
              return watch(`${category}.${pipeline}.${key as unknown as string}` as any)
            },
            setConfigValue(key, value) {
              setValue(`${category}.${pipeline}.${key as unknown as string}` as any, value)
            }
          })}
        </div>
      )}
    </>
  )
}
