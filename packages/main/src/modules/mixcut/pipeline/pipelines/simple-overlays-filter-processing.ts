import {
  MixcutPipeline,
  MixcutPipelineContext,
  SingleOverlayProcessor
} from '@/modules/mixcut/pipeline/mixcut-pipeline.types.js'
import { Logger } from '@nestjs/common'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'

export class SimpleOverlaysFilterProcessingPipeline extends MixcutPipeline {

  private readonly logger = new Logger(SimpleOverlaysFilterProcessingPipeline.name)

  private processor?: SingleOverlayProcessor = undefined
  private filter?: (o: MixcutableOverlay) => boolean = undefined

  private constructor(context: MixcutPipelineContext) {
    super(context)
  }

  static build(
    context: MixcutPipelineContext,
    processorBuilder: new (pipeline: MixcutPipeline) => SingleOverlayProcessor,
    filter: (o: MixcutableOverlay) => boolean,
  ) {
    const instance = new SimpleOverlaysFilterProcessingPipeline(context)
    instance.processor = new processorBuilder(instance)
    instance.filter = filter

    return instance
  }

  public async process(overlays: MixcutableOverlay[]): Promise<MixcutableOverlay[]> {
    if (!this.processor || !this.filter) {
      this.logger.warn('Processor not set')
      return overlays
    }

    return Promise.all(
      overlays
        .filter(this.filter)
        .map(overlay => this.processor!.process(overlay))
    )
  }
}
