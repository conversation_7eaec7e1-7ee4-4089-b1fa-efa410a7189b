import { EditorState } from '@/libs/cache/parts/editor.cache'
import { useCallback, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { Overlay, OverlayType, RenderableOverlay, VideoOverlay } from '@app/shared/types/overlay'
import { MixcutableOverlay, MixcutIPC, RenderRequestPayload } from '@app/shared/types/ipc/mixcut'
import { generateRenderRequestPayload } from '@/modules/mixcut/utils'
import { calculateDuration, calculateRenderableOverlays } from '@/modules/video-editor/utils/overlay-helper'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { sleep } from '@app/shared/utils'
import { IndexableTrack, Track, TrackType } from '@/modules/video-editor/types'
import { getStoryboards } from '@/modules/video-editor/utils/track-helper'

import { DuplicateRateFilter, GeneratedMixcut, MixcutContextValues, PreviewableMixcut } from './context'
import { useMixcutRulesForm } from './useMixcutRulesForm'
import { useMultiSelection } from './useMultiSelection'
import { omit } from 'lodash'

function generateMatrixByTracks(storyboards: Overlay[], tracks: IndexableTrack[]) {
  return storyboards
    .map((_, storyboardIndex) => {
      return tracks
        .filter(t => t.overlays.some(o => o.storyboardIndex === storyboardIndex))
        .map(o => o.index)
    })
}

function generateComboFromMatrix(matrix: number[][]): number[] {
  return matrix.map(column =>
    column[Math.floor(Math.random() * column.length)]
  )
}

function createMapFromOverlayToTrackIndex(tracks: Track[]) {
  return tracks.reduce((result, track, index) => {
    track.overlays.forEach(overlay => {
      result[overlay.id] = index
    })
    return result
  }, {} as Record<number, number>)
}

async function createMixcuts(tracks: Track[], generateCount: number): Promise<GeneratedMixcut[]> {
  const { videoTracks, narrationTracks } = tracks.reduce(
    (result, track, trackIndex) => {
      if (!track.isGlobalTrack && track.type === TrackType.VIDEO) {
        result.videoTracks.push({ ...track, index: trackIndex })
      }
      if (!track.isGlobalTrack && track.type === TrackType.NARRATION) {
        result.narrationTracks.push({ ...track, index: trackIndex })
      }

      return result
    },
    {
      videoTracks: [] as IndexableTrack[],
      narrationTracks: [] as IndexableTrack[],
    }
  )

  const storyboards = getStoryboards(tracks)
  const videoMatrix = generateMatrixByTracks(storyboards, videoTracks)
  const narrationMatrix = generateMatrixByTracks(storyboards, narrationTracks)

  const combos = await window.mixcut.generateCombos({
    limit: generateCount,
    threshold: 1,
    matrix: videoMatrix,
  })

  return combos.map(combo => ({
    videoCombo: combo,
    narrationSelections: generateComboFromMatrix(narrationMatrix)
  }))
}

function createMixcutableOverlays(
  tracks: Track[],
  trackIndexByOverlay: Record<number, number>,
  overlays: RenderableOverlay[],
): MixcutableOverlay[] {
  return overlays.map(overlay => {
    const trackIndex = trackIndexByOverlay[overlay.id]

    return {
      ...overlay,
      trackIndex,
      isNarrationText: tracks[trackIndex].type === TrackType.NARRATION,
      // storyboardInfo: {
      //   index: overlay.storyboardIndex,
      //   from: overlay.from,
      //   durationInFrames: overlay.durationInFrames,
      // }
    }
  })
}

export const useGenerationPart = (state: EditorState, scriptId: string): MixcutContextValues['generation'] => {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [generateCount, setGenerateCount] = useState(10)
  const [generatedMixcuts, setGeneratedMixcuts] = useState<PreviewableMixcut[]>([])

  const [batchUploadState, setBatchUploadState] = useState<MixcutContextValues['generation']['batchUploadState']>({
    visible: false,
    completed: 0,
    total: 0,
  })

  const [duplicateRateFilter, setDuplicateRateFilter] = useState<DuplicateRateFilter>({
    minRate: 0,
    maxRate: 100
  })

  const rulesForm = useMixcutRulesForm()

  const multiSelection = useMultiSelection(generatedMixcuts)

  // 批量上传方法
  const uploadSelectedPreviews = useCallback(
    async () => {
      const { selectedIndices, setSelectedIndices } = multiSelection

      const selectedIndicesArray = Array.from(selectedIndices)
      if (selectedIndicesArray.length === 0) {
        toast.warning('请先选择要上传的混剪结果')
        return
      }

      setBatchUploadState({
        visible: true,
        completed: 0,
        total: selectedIndicesArray.length,
      })

      try {
        for (let i = 0; i < selectedIndicesArray.length; i++) {
          const index = selectedIndicesArray[i]
          const mixcut = generatedMixcuts[index]

          setBatchUploadState(prev => ({
            ...prev,
            currentItem: `组合 [${mixcut.videoCombo.selections.join(', ')}]`
          }))

          // 获取第一个视频overlay用于封面
          const { tracks, playerMetadata } = state
          const firstStoryboardTrackIndex = mixcut.videoCombo.selections[0]
          const targetVideoTrack = tracks[firstStoryboardTrackIndex]
          const firstVideoOverlay = targetVideoTrack?.overlays.find(
            overlay => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
          ) as VideoOverlay | null

          const data: RenderRequestPayload = generateRenderRequestPayload(tracks, mixcut, playerMetadata)

          await Promise.all([
            window.mixcut.uploadMixcutResult({
              scriptId,
              data,
              similarity: mixcut.videoCombo.similarity,
              cover: firstVideoOverlay?.originalMeta.coverUrl,
              duration: calculateDuration(data.inputProps.overlays)
            }),
            new Promise(resolve => setTimeout(resolve, 1500))
          ])

          setBatchUploadState(prev => ({
            ...prev,
            completed: i + 1
          }))
        }

        toast.success(`成功保存 ${selectedIndicesArray.length} 个混剪结果！`)

        setGeneratedMixcuts(prev => {
          return prev.filter((_, index) => !selectedIndicesArray.includes(index))
        })
        setSelectedIndices(new Set())

        void queryClient.refetchQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST] })
        void queryClient.refetchQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_DETAIL] })
        await sleep(1000)
      } catch (error) {
        console.error('批量上传失败:', error)
        toast.error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
      } finally {
        setBatchUploadState({
          visible: false,
          completed: 0,
          total: 0,
        })
      }
    },
    [multiSelection, generatedMixcuts, state]
  )

  const pipelines = useMemo((): MixcutIPC.MixcutPipelineConfig[] => {
    const categories = Object.values(rulesForm.watch())

    return categories
      .map(category => {
        return Object.entries(category)
          .filter(([, config]) => config.enabled)
          .map(([name, config]) => ({
            name,
            config: omit(config, 'enabled')
          }))
      })
      .flat()
  }, [rulesForm.watch()])

  console.log(pipelines)

  const generateCombinations = useCallback(async () => {
    const { tracks, playerMetadata } = state

    const mixcuts = await createMixcuts(tracks, generateCount)

    const trackIndexByOverlay = createMapFromOverlayToTrackIndex(tracks)
    const result: PreviewableMixcut[] = []

    for (const mixcut of mixcuts) {
      const overlays = createMixcutableOverlays(
        tracks,
        trackIndexByOverlay,
        calculateRenderableOverlays(tracks, {
          [TrackType.VIDEO]: mixcut.videoCombo.selections,
          [TrackType.NARRATION]: mixcut.narrationSelections,
        })
      )

      const mixedOverlays = await window.mixcut.executeMixcut({
        context: {
          playerMetadata,
          storyboards: getStoryboards(tracks),
        },
        overlays,
        pipelines,
      })
      result.push({ ...mixcut, overlays: mixedOverlays })
    }

    setGeneratedMixcuts(result)
    // 清空选择状态
    multiSelection.setSelectedIndices(new Set())
    setDrawerOpen(false)
  }, [state.tracks, generateCount, pipelines])

  return {
    drawerOpen,
    setDrawerOpen,

    generateCount,
    setGenerateCount,

    rulesForm,

    generatedMixcuts,
    batchUploadState,
    uploadSelectedPreviews,
    generateCombinations,

    duplicateRateFilter,
    setDuplicateRateFilter,

    ...multiSelection,
  }
}
