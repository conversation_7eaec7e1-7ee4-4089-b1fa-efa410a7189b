import { MixcutPipeline } from '@/modules/mixcut/pipeline/mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { OverlayType } from '@app/shared/types/overlay.js'
import _ from 'lodash'

export class NarrationTextPositionPipeline extends MixcutPipeline {

  private readonly OFFSET_RANGE = 32

  async process(overlays: MixcutableOverlay[]): Promise<MixcutableOverlay[]> {
    const targetOverlays = this.#filterTargetOverlays(overlays)

    const { minX, minY, maxX, maxY } = this.#calculateOffsetBoundary(targetOverlays)

    const offsetX = _.clamp(
      _.random(-this.OFFSET_RANGE, this.OFFSET_RANGE),
      minX,
      maxX
    )

    const offsetY = _.clamp(
      _.random(-this.OFFSET_RANGE, this.OFFSET_RANGE),
      minY,
      maxY
    )

    return [
      ...overlays.filter(o => !targetOverlays.some(target => target.id === o.id)),
      ...targetOverlays.map(overlay => {
        return {
          ...overlay,
          left: overlay.left + offsetX,
          top: overlay.top + offsetY,
        }
      })
    ]
  }

  #filterTargetOverlays(overlays: MixcutableOverlay[]) {
    return overlays.filter(o => o.type === OverlayType.TEXT && o.isNarrationText)
  }

  #calculateOffsetBoundary(overlays: MixcutableOverlay[]) {
    const boundaryLeft = 0
    const boundaryTop = 0
    const boundaryRight = this.context.playerMetadata.width
    const boundaryBottom = this.context.playerMetadata.height

    const maxBoundaryOfOverlays = overlays.reduce((max, overlay) => {
      const right = overlay.left + overlay.width
      const bottom = overlay.top + overlay.height
      return {
        left: Math.min(max.left, overlay.left),
        top: Math.min(max.top, overlay.top),
        right: Math.max(max.right, right),
        bottom: Math.max(max.bottom, bottom)
      }
    }, {
      left: Infinity,
      top: Infinity,
      right: -Infinity,
      bottom: -Infinity
    })

    return {
      minX: boundaryLeft - maxBoundaryOfOverlays.left,
      minY: boundaryTop - maxBoundaryOfOverlays.top,
      maxX: boundaryRight - maxBoundaryOfOverlays.right,
      maxY: boundaryBottom - maxBoundaryOfOverlays.bottom
    }
  }
}
